/**
 * @file Test_Grayscale_Sensor.c
 * @brief 灰度传感器测试和验证程序
 * @version 1.0
 * @date 2025-08-01
 * 
 * 用于验证灰度传感器修复后的功能
 */

#include "SysConfig.h"

// 测试用的传感器实例
extern No_MCU_Sensor GraySensor;
extern unsigned short Gray_Anolog[8];
extern unsigned char Gray_Digtal;

/**
 * @brief 基础ADC读取测试
 * 测试单个ADC通道的读取功能
 */
void Test_BasicADC(void)
{
    unsigned int adc_value;
    
    // 测试10次ADC读取
    for(int i = 0; i < 10; i++) {
        adc_value = adc_getValue();
        
        // 检查ADC值是否在合理范围内（12位ADC：0-4095）
        if(adc_value > 4095) {
            // ADC值超出范围，可能存在问题
            return;
        }
        
        // 添加小延时
        for(volatile int delay = 0; delay < 1000; delay++);
    }
}

/**
 * @brief GPIO地址线测试
 * 测试地址线切换功能
 */
void Test_AddressLines(void)
{
    // 测试所有8个地址组合
    for(int addr = 0; addr < 8; addr++) {
        // 设置地址线
        Switch_Address_0(!(addr & 0x01));
        Switch_Address_1(!(addr & 0x02));
        Switch_Address_2(!(addr & 0x04));
        
        // 稳定延时
        for(volatile int delay = 0; delay < GPIO_SWITCH_DELAY; delay++);
        
        // 读取ADC值
        unsigned int adc_val = adc_getValue();
        
        // 检查读取是否成功
        if(adc_val > 4095) {
            // 地址线可能有问题
            return;
        }
    }
}

/**
 * @brief 传感器数据采集测试
 * 测试完整的8通道数据采集
 */
void Test_SensorDataCollection(void)
{
    unsigned short test_result[8];
    
    // 执行数据采集
    Get_Analog_value(test_result);
    
    // 验证数据有效性
    int valid_channels = 0;
    for(int i = 0; i < 8; i++) {
        // 检查数据是否在合理范围内
        if(test_result[i] >= 50 && test_result[i] <= 4000) {
            valid_channels++;
        }
    }
    
    // 至少应该有4个通道有有效数据
    if(valid_channels < 4) {
        // 数据采集可能存在问题
        return;
    }
}

/**
 * @brief 传感器初始化测试
 * 测试传感器初始化功能
 */
void Test_SensorInitialization(void)
{
    No_MCU_Sensor test_sensor;
    
    // 执行初始化
    No_MCU_Ganv_Sensor_Init_Frist(&test_sensor);
    
    // 检查初始化结果
    if(!test_sensor.ok) {
        // 初始化失败
        return;
    }
    
    // 检查阈值设置
    for(int i = 0; i < 8; i++) {
        if(test_sensor.Calibrated_white[i] <= test_sensor.Calibrated_black[i]) {
            // 阈值设置错误
            return;
        }
    }
}

/**
 * @brief 二值化处理测试
 * 测试模拟值到数字值的转换
 */
void Test_DigitalConversion(void)
{
    unsigned short test_analog[8] = {100, 500, 1000, 1500, 2000, 2500, 3000, 3500};
    unsigned short white_thresh[8] = {3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000};
    unsigned short black_thresh[8] = {200, 200, 200, 200, 200, 200, 200, 200};
    unsigned char digital_result = 0;
    
    // 执行二值化转换
    convertAnalogToDigital(test_analog, white_thresh, black_thresh, &digital_result);
    
    // 验证转换结果
    // 前几个值应该被识别为黑线（置1），后几个值应该被识别为白色（置0）
    if((digital_result & 0x0F) == 0) {
        // 转换可能有问题
        return;
    }
}

/**
 * @brief 完整功能测试
 * 测试传感器的完整工作流程
 */
void Test_CompleteFunctionality(void)
{
    // 确保传感器已初始化
    if(!GraySensor.ok) {
        No_MCU_Ganv_Sensor_Init_Frist(&GraySensor);
    }
    
    // 执行完整的传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    
    // 获取结果
    Get_Anolog_Value(&GraySensor, Gray_Anolog);
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);
    
    // 验证结果
    int valid_data = 0;
    for(int i = 0; i < 8; i++) {
        if(Gray_Anolog[i] > 50 && Gray_Anolog[i] < 4000) {
            valid_data++;
        }
    }
    
    if(valid_data < 4) {
        // 功能测试失败
        return;
    }
}

/**
 * @brief 性能测试
 * 测试传感器读取的性能和稳定性
 */
void Test_Performance(void)
{
    uint32_t start_time = Sys_GetTick();
    
    // 连续执行100次传感器读取
    for(int i = 0; i < 100; i++) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    }
    
    uint32_t end_time = Sys_GetTick();
    uint32_t duration = end_time - start_time;
    
    // 检查性能是否合理（100次读取应该在合理时间内完成）
    if(duration > 5000) { // 超过5秒认为性能有问题
        return;
    }
}

/**
 * @brief 主测试函数
 * 执行所有测试项目
 */
void Test_GrayscaleSensor_Main(void)
{
    // 依次执行各项测试
    Test_BasicADC();
    Test_AddressLines();
    Test_SensorInitialization();
    Test_SensorDataCollection();
    Test_DigitalConversion();
    Test_CompleteFunctionality();
    Test_Performance();
    
    // 测试完成，传感器应该正常工作
}

/**
 * @brief 调试信息输出
 * 输出传感器的详细状态信息（如果启用调试模式）
 */
void Debug_PrintSensorStatus(void)
{
    #if SENSOR_DEBUG_MODE
    // 这里可以添加调试输出代码
    // 例如通过UART输出传感器状态
    #endif
}
