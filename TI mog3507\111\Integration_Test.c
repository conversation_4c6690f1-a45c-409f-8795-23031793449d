/**
 * @file Integration_Test.c
 * @brief 灰度传感器修复后的集成测试
 * @version 1.0
 * @date 2025-08-01
 * 
 * 用于验证修复后的灰度传感器与整个系统的集成效果
 */

#include "SysConfig.h"

// 外部变量声明
extern No_MCU_Sensor GraySensor;
extern unsigned short Gray_Anolog[8];
extern unsigned char Gray_Digtal;

// 测试结果统计
typedef struct {
    uint32_t total_tests;
    uint32_t passed_tests;
    uint32_t failed_tests;
    uint32_t adc_errors;
    uint32_t gpio_errors;
    uint32_t data_errors;
} TestResults_t;

static TestResults_t test_results = {0};

/**
 * @brief 初始化测试环境
 */
void Integration_Test_Init(void)
{
    // 清零测试结果
    memset(&test_results, 0, sizeof(TestResults_t));
    
    // 确保传感器已正确初始化
    No_MCU_Ganv_Sensor_Init_Frist(&GraySensor);
    
    // 等待初始化完成
    for(volatile int i = 0; i < 10000; i++);
}

/**
 * @brief 测试ADC基础功能
 * @return 1=成功, 0=失败
 */
uint8_t Test_ADC_Basic(void)
{
    test_results.total_tests++;
    
    // 测试多次ADC读取
    for(int i = 0; i < 10; i++) {
        unsigned int adc_val = adc_getValue();
        
        // 检查ADC值是否在合理范围
        if(adc_val > 4095) {
            test_results.adc_errors++;
            test_results.failed_tests++;
            return 0;
        }
        
        // 检查ADC值是否有变化（不应该总是相同）
        static unsigned int last_val = 0;
        if(i > 0 && adc_val == last_val) {
            // 连续相同值可能表示ADC有问题
        }
        last_val = adc_val;
    }
    
    test_results.passed_tests++;
    return 1;
}

/**
 * @brief 测试GPIO地址线功能
 * @return 1=成功, 0=失败
 */
uint8_t Test_GPIO_Address(void)
{
    test_results.total_tests++;
    
    unsigned int adc_values[8];
    
    // 测试所有8个地址组合
    for(int addr = 0; addr < 8; addr++) {
        // 设置地址线
        Switch_Address_0(!(addr & 0x01));
        Switch_Address_1(!(addr & 0x02));
        Switch_Address_2(!(addr & 0x04));
        
        // 稳定延时
        for(volatile int delay = 0; delay < GPIO_SWITCH_DELAY; delay++);
        
        // 读取ADC值
        adc_values[addr] = adc_getValue();
        
        // 检查读取是否成功
        if(adc_values[addr] > 4095) {
            test_results.gpio_errors++;
            test_results.failed_tests++;
            return 0;
        }
    }
    
    // 检查不同地址是否产生不同的值（至少有一些差异）
    uint8_t has_variation = 0;
    for(int i = 1; i < 8; i++) {
        if(abs((int)adc_values[i] - (int)adc_values[0]) > 50) {
            has_variation = 1;
            break;
        }
    }
    
    if(!has_variation) {
        test_results.gpio_errors++;
        test_results.failed_tests++;
        return 0;
    }
    
    test_results.passed_tests++;
    return 1;
}

/**
 * @brief 测试完整的传感器数据采集
 * @return 1=成功, 0=失败
 */
uint8_t Test_Sensor_DataCollection(void)
{
    test_results.total_tests++;
    
    // 执行完整的传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    
    // 获取数据
    if(!Get_Anolog_Value(&GraySensor, Gray_Anolog)) {
        test_results.data_errors++;
        test_results.failed_tests++;
        return 0;
    }
    
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);
    
    // 验证模拟数据
    int valid_channels = 0;
    for(int i = 0; i < 8; i++) {
        if(Gray_Anolog[i] >= 50 && Gray_Anolog[i] <= 4000) {
            valid_channels++;
        }
    }
    
    if(valid_channels < 4) {
        test_results.data_errors++;
        test_results.failed_tests++;
        return 0;
    }
    
    test_results.passed_tests++;
    return 1;
}

/**
 * @brief 测试传感器稳定性
 * @return 1=成功, 0=失败
 */
uint8_t Test_Sensor_Stability(void)
{
    test_results.total_tests++;
    
    uint32_t start_time = Sys_GetTick();
    uint8_t stable_readings = 0;
    
    // 连续测试50次
    for(int i = 0; i < 50; i++) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
        Get_Anolog_Value(&GraySensor, Gray_Anolog);
        
        // 检查数据稳定性
        int valid_data = 0;
        for(int j = 0; j < 8; j++) {
            if(Gray_Anolog[j] > 50 && Gray_Anolog[j] < 4000) {
                valid_data++;
            }
        }
        
        if(valid_data >= 6) { // 至少6个通道有效
            stable_readings++;
        }
        
        // 添加测试间隔
        for(volatile int delay = 0; delay < 1000; delay++);
    }
    
    uint32_t end_time = Sys_GetTick();
    uint32_t duration = end_time - start_time;
    
    // 检查稳定性（至少80%的读取应该是稳定的）
    if(stable_readings < 40) {
        test_results.data_errors++;
        test_results.failed_tests++;
        return 0;
    }
    
    // 检查性能（50次读取应该在合理时间内完成）
    if(duration > 3000) { // 超过3秒认为性能有问题
        test_results.failed_tests++;
        return 0;
    }
    
    test_results.passed_tests++;
    return 1;
}

/**
 * @brief 测试二值化功能
 * @return 1=成功, 0=失败
 */
uint8_t Test_Digital_Conversion(void)
{
    test_results.total_tests++;
    
    // 执行传感器读取
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);
    
    // 检查数字输出是否合理（不应该全为0或全为1）
    if(Gray_Digtal == 0x00 || Gray_Digtal == 0xFF) {
        // 可能存在问题，但不一定是错误（取决于实际环境）
        // 这里只记录，不算作失败
    }
    
    test_results.passed_tests++;
    return 1;
}

/**
 * @brief 执行完整的集成测试
 * @return 测试通过的百分比 (0-100)
 */
uint8_t Integration_Test_Run(void)
{
    // 初始化测试环境
    Integration_Test_Init();
    
    // 执行各项测试
    Test_ADC_Basic();
    Test_GPIO_Address();
    Test_Sensor_DataCollection();
    Test_Sensor_Stability();
    Test_Digital_Conversion();
    
    // 计算通过率
    if(test_results.total_tests == 0) {
        return 0;
    }
    
    uint8_t pass_rate = (test_results.passed_tests * 100) / test_results.total_tests;
    return pass_rate;
}

/**
 * @brief 获取测试结果
 * @return 测试结果结构体指针
 */
TestResults_t* Integration_Test_GetResults(void)
{
    return &test_results;
}

/**
 * @brief 打印测试结果（调试用）
 */
void Integration_Test_PrintResults(void)
{
    #if SENSOR_DEBUG_MODE
    // 这里可以添加测试结果的输出代码
    // 例如通过UART输出详细的测试报告
    #endif
}

/**
 * @brief 快速验证传感器是否工作正常
 * @return 1=正常, 0=异常
 */
uint8_t Integration_Test_QuickCheck(void)
{
    // 快速检查：执行一次完整读取
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    Get_Anolog_Value(&GraySensor, Gray_Anolog);
    
    // 检查是否有合理的数据
    int valid_count = 0;
    for(int i = 0; i < 8; i++) {
        if(Gray_Anolog[i] > 50 && Gray_Anolog[i] < 4000) {
            valid_count++;
        }
    }
    
    return (valid_count >= 4) ? 1 : 0;
}
