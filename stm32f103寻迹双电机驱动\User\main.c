#include "stm32f10x.h"
#include "stm32f10x_tim.h"
#include "OLED.h"
#include "Timer.h"
#include "Motor.h"
#include "MotorRun.h"
#include "Encoder.h"
#include "ADC.h"
#include "Grayscale.h"
#include "LineTracking.h"

// STM32F103寻迹机器人主程序
// 功能：双电机差速控制、8路灰度传感器寻迹、OLED实时显示

// 电机控制变量
int16_t Speed;                     // 右电机实际速度
int16_t TargetSpeed = 22;          // 右电机目标速度，默认22%(补偿电机差异)
int16_t LeftSpeed;                 // 左电机实际速度
int16_t LeftTargetSpeed = 20;      // 左电机目标速度，默认20%
uint8_t SpeedControlActive = 1;    // 速度控制激活标志

// 差速控制变量
int16_t TurnRate = 0;              // 转向率：正值右转，负值左转

// 显示变量
int16_t LeftSpeedPercent = 0;      // 左电机速度百分比显示
int16_t RightSpeedPercent = 0;     // 右电机速度百分比显示

// 传感器变量
Grayscale_Sensor grayscale_sensor; // 8路灰度传感器数据结构

// 启动控制变量
uint16_t startup_delay_counter = 0; // 启动延时计数器
uint8_t system_ready = 0;          // 系统就绪标志：0=延时中，1=可运行

// 寻迹控制接口函数，供寻迹算法调用

// 直接设置左右电机速度，用于精确控制
void SetMotorSpeed(int16_t leftSpeed, int16_t rightSpeed) {
	LeftTargetSpeed = leftSpeed;
	TargetSpeed = rightSpeed;
	TurnRate = 0;              // 直接设速度时清零转向率
	SpeedControlActive = 1;    // 激活电机控制
}

// 差速转向控制，用于寻迹算法
void SetDifferentialTurn(int16_t baseSpeed, int16_t turnRate) {
	TargetSpeed = baseSpeed;
	LeftTargetSpeed = baseSpeed;
	TurnRate = turnRate;       // 设置转向率
	SpeedControlActive = 1;    // 激活电机控制
}

// 停止所有电机
void StopMotors(void) {
	TargetSpeed = 0;
	LeftTargetSpeed = 0;
	TurnRate = 0;
	SpeedControlActive = 0;    // 停止电机控制
}
 
// 主函数：系统初始化和主循环控制
int main(void)
{
	// 系统初始化
	OLED_Init();                                    // OLED显示初始化
	Timer_Init();                                   // 定时器初始化(1秒中断)
	Motor_Init();                                   // 双电机系统初始化
	Encoder_Init();                                 // 编码器初始化

	// 传感器系统初始化
	ADC_Config();                                   // ADC配置
	Grayscale_Init();                               // 灰度传感器初始化
	Grayscale_Sensor_Init_First(&grayscale_sensor); // 传感器校准数据初始化

	// 寻迹算法初始化
	LineTracking_Init();

	// 主循环：系统运行控制
	while (1)
	{
		if(system_ready)  // 系统就绪后开始运行
		{
			if(SpeedControlActive)  // 电机控制激活
			{
				if(TurnRate == 0)  // 直行模式
				{
					if(TargetSpeed > 0)
					{
						dual_run(LeftTargetSpeed, TargetSpeed);  // 前进
					}
					else if(TargetSpeed < 0)
					{
						dual_backrun(-LeftTargetSpeed, -TargetSpeed);  // 后退
					}
					else
					{
						dual_stop();  // 停止
					}
				}
				else  // 差速转向模式
				{
					differential_turn(TargetSpeed, TurnRate);  // 差速转向
				}
			}
			else
			{
				dual_stop();  // 控制未激活时停止
			}

			// 传感器数据处理
			Grayscale_ReadAll(&grayscale_sensor);      // 读取传感器数据
			LineTracking_Process(&grayscale_sensor);   // 寻迹算法处理

			// OLED实时显示系统状态
			OLED_ShowString(1, 1, "L:");                              // 左电机标签
			OLED_ShowSignedNum(1, 3, LeftSpeedPercent, 4);           // 左电机速度百分比
			OLED_ShowString(1, 7, "%");
			OLED_ShowString(2, 1, "R:");                              // 右电机标签
			OLED_ShowSignedNum(2, 3, RightSpeedPercent, 4);          // 右电机速度百分比
			OLED_ShowString(2, 7, "%");

			// 显示传感器状态
			uint8_t sensor_digital = Grayscale_GetDigital(&grayscale_sensor);
			OLED_ShowString(3, 1, "S:");                              // 传感器标签
			OLED_ShowBinNum(3, 3, sensor_digital, 8);                // 8位二进制传感器状态
			OLED_ShowString(4, 1, "        ");                        // 第4行预留
		}
		else  // 启动延时期间
		{
			dual_stop();  // 确保电机停止

			// 显示启动倒计时
			OLED_ShowString(1, 1, "Starting...");
			OLED_ShowString(2, 1, "Wait: ");
			OLED_ShowSignedNum(2, 7, (2 - startup_delay_counter), 2); // 剩余秒数
			OLED_ShowString(2, 9, "s");
			OLED_ShowString(3, 1, "        ");                        // 清空显示
			OLED_ShowString(4, 1, "        ");
		}
	}
}
 
// 定时器2中断处理函数，每1秒触发一次
void TIM2_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM2, TIM_IT_Update) == SET)
	{
		// 启动延时控制(2秒延时)
		if(!system_ready)
		{
			startup_delay_counter++;
			if(startup_delay_counter >= 2)  // 2秒延时结束
			{
				system_ready = 1;  // 系统就绪，开始正常运行
			}
		}

		// 读取编码器速度反馈
		Speed = Encoder_Get();       // 右电机速度(硬件编码器)
		LeftSpeed = Encoder2_Get();  // 左电机速度(软件编码器)

		// 计算显示用的速度百分比
		if(SpeedControlActive)  // 电机控制激活时
		{
			if(TurnRate == 0)  // 直行模式
			{
				RightSpeedPercent = TargetSpeed;      // 显示目标速度
				LeftSpeedPercent = LeftTargetSpeed;
			}
			else  // 差速转向模式
			{
				int16_t CurrentBase = TargetSpeed;
				int16_t LeftCalc = CurrentBase - TurnRate;   // 左轮速度计算
				int16_t RightCalc = CurrentBase + TurnRate;  // 右轮速度计算

				// 限制速度范围在-100到100之间
				if(LeftCalc < -100) LeftCalc = -100;
				if(LeftCalc > 100) LeftCalc = 100;
				if(RightCalc < -100) RightCalc = -100;
				if(RightCalc > 100) RightCalc = 100;

				LeftSpeedPercent = LeftCalc;
				RightSpeedPercent = RightCalc;
			}
		}
		else  // 电机控制未激活
		{
			RightSpeedPercent = 0;  // 显示停止状态
			LeftSpeedPercent = 0;
		}

		TIM_ClearITPendingBit(TIM2, TIM_IT_Update);  // 清除中断标志
	}
}
