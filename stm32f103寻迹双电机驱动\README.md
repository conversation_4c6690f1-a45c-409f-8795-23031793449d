# STM32F103寻迹机器人使用指南

## 项目简介
基于STM32F103C8的智能寻迹机器人，具备双电机差速控制、8路灰度传感器寻迹和OLED实时显示功能。

## 快速开始

### 1. 硬件准备
确保以下硬件连接正确：
- **左电机**：PB0/PB1(方向) + PB6(PWM)
- **右电机**：PA4/PA5(方向) + PA6(PWM)
- **编码器**：左电机PA0/PA1，右电机PA8/PA9
- **OLED显示**：PB8/PB9(I2C)
- **传感器**：8路灰度传感器通过ADC读取

### 2. 编译和烧录
1. 用Keil uVision 5打开项目文件
2. 编译项目 (F7)
3. 连接ST-Link调试器
4. 烧录到芯片 (F8)

### 3. 运行效果
- 上电后2秒延时，OLED显示倒计时
- 系统自动开始低速直行(左轮20%，右轮22%)
- OLED实时显示电机速度和传感器状态

## 核心接口

### 基本运动控制
```c
// 基础运动函数(速度范围0-100%)
dual_run(leftSpeed, rightSpeed);        // 双电机前进
dual_backrun(leftSpeed, rightSpeed);    // 双电机后退
dual_stop();                            // 双电机停止
differential_turn(baseSpeed, turnRate); // 差速转向

// 高级控制接口
SetMotorSpeed(leftSpeed, rightSpeed);     // 直接设置左右电机速度
SetDifferentialTurn(baseSpeed, turnRate); // 设置基础速度和转向率
StopMotors();                             // 停止所有电机
```

### 寻迹算法接口
```c
LineTracking_Process(&sensor);            // 寻迹主处理函数
LineTracking_SetLostLineTimeout(40);      // 设置丢线保持时间
```

## OLED显示说明

### 启动阶段显示
```
Starting...     [启动提示]
Wait: 2s        [倒计时]
```

### 运行阶段显示
```
L:  20%         [左电机速度]
R:  22%         [右电机速度]
S:11000110      [传感器状态，1=检测到黑线]
```

## 参数调节

### 寻迹参数调整
如果寻迹效果不理想，可以调整以下参数：

1. **丢线保持时间** (在main.c中调用)
```c
LineTracking_SetLostLineTimeout(30);  // 减少保持时间，默认40
```

2. **寻迹算法参数** (在Hardware/LineTracking.c中修改)
```c
#define KP_FACTOR 3          // 比例系数，控制转向强度
#define MAX_TURN_RATE 20     // 最大转向率，限制转向幅度
#define BASE_SPEED 30        // 基础速度百分比
```

### 电机速度调整
在main.c中修改默认速度：
```c
int16_t TargetSpeed = 22;        // 右电机默认速度
int16_t LeftTargetSpeed = 20;    // 左电机默认速度
```

## 项目结构
```
├── Hardware/           # 硬件驱动层
│   ├── Motor.c/h      # 电机GPIO控制
│   ├── MotorRun.c/h   # 运动控制逻辑
│   ├── LineTracking.c/h # 寻迹算法
│   ├── Grayscale.c/h  # 灰度传感器
│   ├── OLED.c/h       # 显示驱动
│   └── PWM.c/h        # PWM调速
├── User/              # 应用层
│   └── main.c         # 主程序和控制逻辑
└── System/            # 系统层
    └── Delay.c/h      # 延时函数
```

## 常见问题

### Q: 机器人不走直线怎么办？
A: 调整main.c中的默认速度，通常右轮需要比左轮快2-3%来补偿电机差异。

### Q: 寻迹转弯太急怎么调节？
A: 减小LineTracking.c中的KP_FACTOR值(默认3)或MAX_TURN_RATE值(默认20)。

### Q: 经常丢线怎么办？
A: 增加丢线保持时间：`LineTracking_SetLostLineTimeout(50);`

### Q: OLED显示异常？
A: 检查PB8/PB9的I2C连接，确保OLED地址正确。

## 开发环境
- **IDE**: Keil uVision 5
- **芯片**: STM32F103C8T6
- **调试器**: ST-Link V2
- **编程语言**: C语言

---
*适合STM32初学者学习嵌入式开发和机器人控制算法*
