/**
 * @file Task_App.c
 * @brief 电机控制任务实现层
 * @version 0.1
 * @date 2025-07-12
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "Task_App.h"

/*Data Motor*/
int16_t Data_MotorEncoder[2] = {0}; //电机的编码值（用于中断处理）
MOTOR_Def_t *Motor[2] = {&Motor_Left, &Motor_Right}; //电机实例

/*Data Grayscale Sensor*/
No_MCU_Sensor GraySensor; //灰度传感器实例
unsigned short Gray_Anolog[8]; //8路传感器模拟值
unsigned char Gray_Digtal; //8位数字输出（每位对应一个传感器）

// 电机测试状态
typedef enum {
    MOTOR_TEST_STOP = 0,
    MOTOR_TEST_FORWARD,
    MOTOR_TEST_BACKWARD,
    MOTOR_TEST_LEFT_TURN,
    MOTOR_TEST_RIGHT_TURN
} MotorTestState_t;

static MotorTestState_t motor_test_state = MOTOR_TEST_FORWARD;
static uint32_t test_start_time = 0;

void Task_Motor_ManualControl(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Interrupt_Init(); //中断初始化

    // 初始化OLED显示屏
    OLED_Init();

    // 初始化灰度传感器（使用预设阈值：白1800，黑300）
    No_MCU_Ganv_Sensor_Init_Frist(&GraySensor);

    // 添加OLED显示任务 - 50ms周期，优先级5
    Task_Add("OLED", Task_OLED, 50, NULL, 5);

    // 添加灰度传感器任务 - 10ms周期，优先级2（较高优先级）
    Task_Add("GraySensor", Task_GraySensor, 10, NULL, 2);

    // 设置电机恒定速度 - 可以在这里修改速度和方向
    Motor_SetBothSpeed(20, 20);  // 60%速度恒定前进 - 修改这里的数值来改变速度
}

//空闲任务函数
void Task_IdleFunction(void)
{
    // 空闲时可以执行一些低优先级任务
}

//电机手动控制测试任务 2000ms
void Task_Motor_ManualControl(void *para)
{
    static uint8_t test_cycle = 0;

    switch (test_cycle % 5)
    {
        case 0: // 停止
            Motor_StopAll();
            break;

        case 1: // 前进 - 两个电机都正转
            Motor_SetBothSpeed(50, 50);  // 50%速度前进
            break;

        case 2: // 后退 - 两个电机都反转
            Motor_SetBothSpeed(-50, -50); // 50%速度后退
            break;

        case 3: // 左转 - 左电机慢，右电机快
            Motor_SetBothSpeed(20, 60);   // 左转
            break;

        case 4: // 右转 - 左电机快，右电机慢
            Motor_SetBothSpeed(60, 20);   // 右转
            break;
    }

    test_cycle++;
}

// 用于计算实际速度的静态变量
static int16_t last_encoder_left = 0;
static int16_t last_encoder_right = 0;
static uint32_t last_time = 0;

/**
 * @brief OLED显示任务 - 50ms周期
 * @param para 任务参数（未使用）
 */
void Task_OLED(void *para)
{
    // 获取当前时间和编码器值
    uint32_t current_time = Sys_GetTick();
    int16_t current_encoder_left = Data_MotorEncoder[0];
    int16_t current_encoder_right = Data_MotorEncoder[1];

    // 计算实际速度（脉冲/秒）
    float left_actual_speed = 0.0f;
    float right_actual_speed = 0.0f;

    if (last_time != 0) {
        // 计算时间间隔（毫秒转秒）
        float time_interval = (current_time - last_time) / 1000.0f;

        if (time_interval > 0) {
            // 计算编码器变化量
            int16_t left_delta = current_encoder_left - last_encoder_left;
            int16_t right_delta = current_encoder_right - last_encoder_right;

            // 计算实际速度（脉冲/秒）
            left_actual_speed = left_delta / time_interval;
            right_actual_speed = right_delta / time_interval;

            // 根据电机方向调整速度符号
            if (Motor_Left.Motor_Dirc == DIRC_BACKWARD) {
                left_actual_speed = -left_actual_speed;
            }
            if (Motor_Right.Motor_Dirc == DIRC_BACKWARD) {
                right_actual_speed = -right_actual_speed;
            }
        }
    }

    // 更新历史值
    last_encoder_left = current_encoder_left;
    last_encoder_right = current_encoder_right;
    last_time = current_time;

    // 第一行显示左电机实际速度（脉冲/秒）
    OLED_Printf(0, 0, 16, "L:%.0f pps", left_actual_speed);

    // 第二行显示右电机实际速度（脉冲/秒）
    OLED_Printf(0, 16, 16, "R:%.0f pps", right_actual_speed);

    // 第三行显示灰度传感器数字输出（8位二进制）
    OLED_Printf(0, 32, 16, "Gray:%c%c%c%c%c%c%c%c",
                (Gray_Digtal & 0x80) ? '1' : '0',  // bit7
                (Gray_Digtal & 0x40) ? '1' : '0',  // bit6
                (Gray_Digtal & 0x20) ? '1' : '0',  // bit5
                (Gray_Digtal & 0x10) ? '1' : '0',  // bit4
                (Gray_Digtal & 0x08) ? '1' : '0',  // bit3
                (Gray_Digtal & 0x04) ? '1' : '0',  // bit2
                (Gray_Digtal & 0x02) ? '1' : '0',  // bit1
                (Gray_Digtal & 0x01) ? '1' : '0'); // bit0

    // 第四行显示传感器状态和调试信息
    static uint8_t display_mode = 0;
    static uint32_t last_switch_time = 0;
    uint32_t current_time = Sys_GetTick();

    // 每2秒切换显示内容
    if(current_time - last_switch_time > 2000) {
        display_mode = (display_mode + 1) % 3;
        last_switch_time = current_time;
    }

    switch(display_mode) {
        case 0: // 显示前4个传感器的模拟值
            OLED_Printf(0, 48, 16, "A0-3:%d %d %d %d", Gray_Anolog[0], Gray_Anolog[1], Gray_Anolog[2], Gray_Anolog[3]);
            break;
        case 1: // 显示后4个传感器的模拟值
            OLED_Printf(0, 48, 16, "A4-7:%d %d %d %d", Gray_Anolog[4], Gray_Anolog[5], Gray_Anolog[6], Gray_Anolog[7]);
            break;
        case 2: // 显示传感器状态
            OLED_Printf(0, 48, 16, "Sensor:%s", GraySensor.ok ? "OK" : "ERR");
            break;
    }
}

/**
 * @brief 灰度传感器任务 - 10ms周期
 * @param para 任务参数（未使用）
 */
void Task_GraySensor(void *para)
{
    // 检查传感器是否初始化完成
    if (!GraySensor.ok) {
        return; // 传感器未就绪，直接返回
    }

    // 执行传感器数据采集和处理
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);

    // 获取8路传感器模拟值
    if (Get_Anolog_Value(&GraySensor, Gray_Anolog)) {
        // 获取8位数字输出
        Gray_Digtal = Get_Digtal_For_User(&GraySensor);

        // 数据有效性检查
        static uint8_t error_count = 0;
        uint8_t valid_data = 0;

        // 检查是否有合理的ADC值变化
        int i;
        for(i = 0; i < 8; i++) {
            if(Gray_Anolog[i] > 50 && Gray_Anolog[i] < 4000) {
                valid_data++;
            }
        }

        if(valid_data < 4) { // 如果少于4个传感器有合理数据
            error_count++;
            if(error_count > 10) { // 连续10次错误，重新初始化
                No_MCU_Ganv_Sensor_Init_Frist(&GraySensor);
                error_count = 0;
            }
        } else {
            error_count = 0; // 重置错误计数
        }
    }
}

