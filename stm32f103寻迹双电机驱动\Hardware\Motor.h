#ifndef __MOTOR_H__
#define __MOTOR_H__

// 双电机控制模块，提供左右电机的基本控制功能
// 硬件连接：左电机PB0/PB1(方向)+PB6(PWM)，右电机PA4/PA5(方向)+PA6(PWM)

void Motor_Init(void);        // 双电机系统初始化
void Right_moto_go(void);     // 右电机前进
void Right_moto_back(void);   // 右电机后退
void Right_moto_Stop(void);   // 右电机停止

void Left_moto_Init(void);    // 左电机初始化
void Left_moto_go(void);      // 左电机前进
void Left_moto_back(void);    // 左电机后退
void Left_moto_Stop(void);    // 左电机停止

#endif
