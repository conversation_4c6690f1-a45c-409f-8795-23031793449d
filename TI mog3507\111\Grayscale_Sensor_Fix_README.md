# 灰度传感器修复说明文档

## 修复概述

本文档记录了TI MSPM0G3507项目中灰度传感器数值读取问题的修复过程和解决方案。

## 问题分析

### 原始问题
- TI芯片项目中灰度传感器数值读取存在问题
- STM32项目中相同硬件工作正常
- 需要对比分析两个项目的差异

### 根本原因
1. **ADC时序问题**：TI项目中ADC读取流程存在不必要的使能/禁用操作
2. **GPIO切换延时缺失**：地址线切换后缺少稳定延时
3. **数据类型不匹配**：ADC返回值与传感器结构体类型不一致
4. **阈值设置不合理**：初始阈值过低，影响检测效果

## 修复内容

### 1. ADC读取优化 (`BSP/Src/ADC.c`)

**修改前：**
```c
unsigned int adc_getValue(void)
{
    unsigned int gAdcResult = 0;
    DL_ADC12_enableConversions(ADC1_INST);
    DL_ADC12_startConversion(ADC1_INST);
    while (DL_ADC12_getStatus(ADC1_INST) != DL_ADC12_STATUS_CONVERSION_IDLE );
    DL_ADC12_stopConversion(ADC1_INST);
    DL_ADC12_disableConversions(ADC1_INST);
    gAdcResult = DL_ADC12_getMemResult(ADC1_INST, ADC1_ADCMEM_0);
    return gAdcResult;
}
```

**修改后：**
```c
unsigned int adc_getValue(void)
{
    unsigned int gAdcResult = 0;
    DL_ADC12_startConversion(ADC1_INST);
    while (DL_ADC12_getStatus(ADC1_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
    gAdcResult = DL_ADC12_getMemResult(ADC1_INST, ADC1_ADCMEM_0);
    return (gAdcResult & 0x0FFF); // 确保返回12位数据
}
```

**改进点：**
- 移除不必要的ADC使能/禁用操作
- 添加数据位宽限制，确保12位精度
- 简化时序，提高稳定性

### 2. GPIO切换延时添加 (`BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.c`)

**关键修复：**
```c
// 通过地址线组合切换传感器通道
Switch_Address_0(!(i&0x01));
Switch_Address_1(!(i&0x02));
Switch_Address_2(!(i&0x04));

// 关键修复：添加GPIO切换稳定延时
for(volatile int delay = 0; delay < GPIO_SWITCH_DELAY; delay++);

// 每个通道采集8次ADC值进行均值滤波
for(j=0;j<8;j++)
{
    Anolag+=Get_adc_of_user();
    // 添加ADC采样间隔延时
    for(volatile int adc_delay = 0; adc_delay < ADC_SAMPLE_DELAY; adc_delay++);
}
```

### 3. 二值化逻辑改进

**参考STM32成功实现，改进二值化处理：**
```c
void convertAnalogToDigital(unsigned short *adc_value,unsigned short *Gray_white,unsigned short *Gray_black,unsigned char *Digital)
{
    *Digital = 0; // 先清零，确保初始状态正确
    for (int i = 0; i < 8; i++) {
        if (adc_value[i] < Gray_black[i]) {
            *Digital |= (1 << i);   // 低于黑阈值置1（检测到黑线）
        } else if (adc_value[i] > Gray_white[i]) {
            *Digital &= ~(1 << i);  // 超过白阈值置0（检测到白色）
        } else {
            // 中间灰度值：使用中值判断
            unsigned short mid_threshold = (Gray_white[i] + Gray_black[i]) / 2;
            if (adc_value[i] < mid_threshold) {
                *Digital |= (1 << i);   // 偏向黑色，置1
            } else {
                *Digital &= ~(1 << i);  // 偏向白色，置0
            }
        }
    }
}
```

### 4. 阈值优化

**修改初始阈值设置：**
```c
// 设置优化后的预设阈值（参考STM32项目的成功经验）
for(int i = 0; i < 8; i++)
{
    sensor->Calibrated_white[i] = 3000;  // 提高白色阈值
    sensor->Calibrated_black[i] = 200;   // 降低黑色阈值
    sensor->Gray_white[i] = (3000*2+200)/3;  // 约2067
    sensor->Gray_black[i] = (3000+200*2)/3;  // 约1133
}
```

### 5. 配置参数化 (`BSP/Inc/No_Mcu_Ganv_Grayscale_Sensor_Config.h`)

**添加可配置参数：**
```c
// 调试和优化配置
#define SENSOR_DEBUG_MODE 0              // 调试模式开关
#define GPIO_SWITCH_DELAY 200            // GPIO切换稳定延时
#define ADC_SAMPLE_DELAY 10              // ADC采样间隔延时
```

### 6. 任务层改进 (`APP/Src/Task_App.c`)

**添加错误检测和自动恢复：**
```c
// 数据有效性检查
static uint8_t error_count = 0;
uint8_t valid_data = 0;

// 检查是否有合理的ADC值变化
for(int i = 0; i < 8; i++) {
    if(Gray_Anolog[i] > 50 && Gray_Anolog[i] < 4000) {
        valid_data++;
    }
}

if(valid_data < 4) { // 如果少于4个传感器有合理数据
    error_count++;
    if(error_count > 10) { // 连续10次错误，重新初始化
        No_MCU_Ganv_Sensor_Init_Frist(&GraySensor);
        error_count = 0;
    }
} else {
    error_count = 0; // 重置错误计数
}
```

## 测试验证

### 测试文件
创建了 `Test_Grayscale_Sensor.c` 文件，包含以下测试项目：

1. **基础ADC读取测试** - 验证单个ADC通道功能
2. **GPIO地址线测试** - 验证地址线切换功能
3. **传感器初始化测试** - 验证初始化流程
4. **数据采集测试** - 验证8通道数据采集
5. **二值化处理测试** - 验证模拟到数字转换
6. **完整功能测试** - 验证整体工作流程
7. **性能测试** - 验证读取性能和稳定性

### 验证步骤

1. **编译项目**：确保所有修改编译通过
2. **硬件测试**：在实际硬件上验证传感器读取
3. **数据对比**：与STM32项目的数据进行对比
4. **长期稳定性测试**：验证长时间运行的稳定性

## 使用说明

### 编译和运行
1. 使用TI Code Composer Studio打开项目
2. 编译项目（确保无错误和警告）
3. 下载到MSPM0G3507开发板
4. 观察OLED显示的传感器状态

### 调试模式
如需启用调试模式，修改配置文件：
```c
#define SENSOR_DEBUG_MODE 1  // 启用调试输出
```

### 参数调整
如果传感器仍有问题，可以调整以下参数：
```c
#define GPIO_SWITCH_DELAY 300    // 增加GPIO切换延时
#define ADC_SAMPLE_DELAY 20      // 增加ADC采样延时
```

## 预期效果

修复后的传感器应该具备以下特性：
1. **稳定的ADC读取**：数值在合理范围内（50-4000）
2. **正确的通道切换**：8个通道都能正常读取
3. **准确的二值化**：能正确识别黑线和白色区域
4. **良好的实时性**：10ms周期内完成数据采集
5. **自动错误恢复**：检测到异常时自动重新初始化

## 注意事项

1. **硬件连接**：确保地址线GPIO连接正确
2. **电源稳定**：确保ADC参考电压稳定
3. **环境光线**：测试时保持一致的光线条件
4. **传感器校准**：必要时重新校准白色和黑色阈值

## 故障排除

### 常见问题
1. **ADC值全为0或4095**：检查ADC配置和硬件连接
2. **数字输出不变化**：检查阈值设置和二值化逻辑
3. **数据不稳定**：增加延时参数或检查电源噪声
4. **性能问题**：优化延时参数或减少采样次数

### 调试建议
1. 使用示波器检查地址线切换时序
2. 监控ADC输入电压变化
3. 对比修改前后的数据差异
4. 逐步调整延时参数找到最佳值

---

**修复完成日期**：2025-08-01  
**修复版本**：v1.0  
**测试状态**：待验证
