#ifndef __Motor_Control_h
#define __Motor_Control_h

#include "SysConfig.h"

/**
 * @brief 纯手动电机控制接口
 * 提供最简单直接的电机控制函数，完全手动控制，无PID
 */

// 基本控制接口（速度范围：-100~100）
void MotorControl_Stop(void);                                    // 停止所有电机
void MotorControl_Forward(float speed);                          // 前进（speed: 0~100）
void MotorControl_Backward(float speed);                         // 后退（speed: 0~100）
void MotorControl_TurnLeft(float speed);                         // 左转（speed: 0~100）
void MotorControl_TurnRight(float speed);                        // 右转（speed: 0~100）
void MotorControl_SetSpeed(float left_speed, float right_speed); // 分别设置左右电机速度

// 获取当前状态
float MotorControl_GetLeftSpeed(void);   // 获取左电机当前设定速度
float MotorControl_GetRightSpeed(void);  // 获取右电机当前设定速度

#endif
