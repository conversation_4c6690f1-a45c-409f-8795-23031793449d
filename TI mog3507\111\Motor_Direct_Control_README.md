# 纯手动电机控制功能说明

## 概述
本项目已完全移除PID控制，实现纯手动电机控制功能。您可以直接输入速度值来控制电机的正反转和速度，系统响应更直接、更简单。

## 功能特点
- ✅ 纯手动速度控制（-100 ~ +100）
- ✅ 正值正转，负值反转
- ✅ 完全移除PID，无任何PID影响
- ✅ 极简用户接口
- ✅ 支持左右电机独立控制
- ✅ 直接PWM控制，响应迅速
- ✅ 保留编码器中断处理（仅用于计数，不影响控制）

## 文件结构
```
008/
├── BSP/
│   ├── Inc/Motor.h          # 电机底层接口（已更新）
│   └── Src/Motor.c          # 电机底层实现（已更新）
├── APP/
│   ├── Inc/
│   │   ├── Task_App.h       # 任务头文件（已更新）
│   │   └── Motor_Control.h  # 用户控制接口（新增）
│   └── Src/
│       ├── Task_App.c       # 任务实现（已更新）
│       └── Motor_Control.c  # 用户控制实现（新增）
├── Motor_Control_Example.c  # 使用示例（新增）
└── Motor_Direct_Control_README.md  # 本说明文件
```

## 快速开始

### 1. 基本使用
```c
#include "Motor_Control.h"

// 前进，速度50%
MotorControl_Forward(50.0f);

// 后退，速度30%
MotorControl_Backward(30.0f);

// 停止
MotorControl_Stop();
```

### 2. 精确控制
```c
// 分别设置左右电机速度
MotorControl_SetSpeed(60.0f, 60.0f);   // 直线前进
MotorControl_SetSpeed(-50.0f, -50.0f); // 直线后退
MotorControl_SetSpeed(-40.0f, 40.0f);  // 原地左转
MotorControl_SetSpeed(40.0f, -40.0f);  // 原地右转
```

## API 参考

### 基本运动
- `MotorControl_Stop()` - 停止所有电机
- `MotorControl_Forward(speed)` - 前进（speed: 0~100）
- `MotorControl_Backward(speed)` - 后退（speed: 0~100）
- `MotorControl_TurnLeft(speed)` - 左转（speed: 0~100）
- `MotorControl_TurnRight(speed)` - 右转（speed: 0~100）

### 精确控制
- `MotorControl_SetSpeed(left, right)` - 设置左右电机速度（-100~100）

### 状态查询
- `MotorControl_GetLeftSpeed()` - 获取左电机当前速度设定
- `MotorControl_GetRightSpeed()` - 获取右电机当前速度设定

## 速度值说明
- **范围**: -100 ~ +100
- **正值**: 电机正转
- **负值**: 电机反转
- **0**: 电机停止
- **绝对值**: 速度大小（百分比）

## 运动模式示例

### 基本运动
```c
MotorControl_Forward(60);      // 60%速度前进
MotorControl_Backward(40);     // 40%速度后退
MotorControl_TurnLeft(50);     // 50%速度左转
MotorControl_TurnRight(50);    // 50%速度右转
```

### 高级运动
```c
// 原地旋转
MotorControl_SetSpeed(-50, 50);   // 逆时针旋转
MotorControl_SetSpeed(50, -50);   // 顺时针旋转

// 大半径转弯
MotorControl_SetSpeed(30, 80);    // 大半径左转
MotorControl_SetSpeed(80, 30);    // 大半径右转
```

## 当前配置
- 系统已完全移除PID控制
- 测试任务每2秒切换一次运动状态：停止 → 前进 → 后退 → 左转 → 右转
- 所有控制都是纯手动，无任何自动调节

## 注意事项
1. 速度值会自动限制在-100~100范围内
2. 电机响应完全直接，无任何PID调节过程
3. 系统已彻底移除PID相关代码，更简洁高效
4. 所有控制都是实时的，修改速度立即生效
5. 编码器仍然工作，但仅用于计数，不参与速度控制

## 故障排除
1. **电机不转**: 检查电机接线和PWM定时器配置
2. **速度不对**: 确认输入的速度值在有效范围内(-100~100)
3. **方向错误**: 检查电机接线或调整速度值的正负号

## 扩展功能
如需添加更多功能，可以：
1. 在Motor_Control.c中添加新的运动模式函数
2. 修改转向函数的速度比例
3. 添加速度渐变功能
4. 集成传感器反馈

参考Motor_Control_Example.c文件查看更多使用示例。
