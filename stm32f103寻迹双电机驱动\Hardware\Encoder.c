#include "stm32f10x.h"                  // Device header
//测速
void Encoder_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	
    //TIM1的CH1和CH2
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
		
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
	TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStructure.TIM_Period = 65536 - 1;		//ARR
	TIM_TimeBaseInitStructure.TIM_Prescaler = 1 - 1;		//PSC
	TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;
	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseInitStructure);
 
	//在TI1和TI2上计数
	TIM_ICInitTypeDef TIM_ICInitStructure;
	TIM_ICStructInit(&TIM_ICInitStructure);
	TIM_ICInitStructure.TIM_Channel = TIM_Channel_1;
	TIM_ICInitStructure.TIM_ICFilter = 0xF;
	TIM_ICInit(TIM1, &TIM_ICInitStructure);
	TIM_ICInitStructure.TIM_Channel = TIM_Channel_2;
	TIM_ICInitStructure.TIM_ICFilter = 0xF;
	TIM_ICInit(TIM1, &TIM_ICInitStructure);
	//定时器编码器接口配置  //通道不反相
	TIM_EncoderInterfaceConfig(TIM1, TIM_EncoderMode_TI12, TIM_ICPolarity_Rising, TIM_ICPolarity_Rising);
	
	TIM_Cmd(TIM1, ENABLE);
}
 
int16_t Encoder_Get(void)
{
	int16_t Temp;
	Temp = TIM_GetCounter(TIM1);
	TIM_SetCounter(TIM1, 0);
	return Temp;
}

//左电机编码器变量
static volatile int16_t Encoder2_Count = 0;
static uint8_t Encoder2_Last_A = 0;
static uint8_t Encoder2_Last_B = 0;
static uint32_t Encoder2_Last_Time = 0; //防抖时间戳

// 性能监控变量已删除，专注寻迹功能

//左电机编码器初始化 - 外部中断方式
void Encoder2_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);

	//PA0和PA1作为编码器输入
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; //上拉输入
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	//配置PA0为外部中断EXTI0
	GPIO_EXTILineConfig(GPIO_PortSourceGPIOA, GPIO_PinSource0);

	EXTI_InitTypeDef EXTI_InitStructure;
	EXTI_InitStructure.EXTI_Line = EXTI_Line0;
	EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
	EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising_Falling; //双边沿触发
	EXTI_InitStructure.EXTI_LineCmd = ENABLE;
	EXTI_Init(&EXTI_InitStructure);

	//配置NVIC
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannel = EXTI0_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1; //高优先级
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	//初始化编码器状态
	Encoder2_Last_A = GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0);
	Encoder2_Last_B = GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1);
	Encoder2_Count = 0;
}

//左电机编码器读取 - 中断方式
int16_t Encoder2_Get(void)
{
	//返回并清零计数（原子操作）
	__disable_irq(); //关闭中断
	int16_t Temp = Encoder2_Count;
	Encoder2_Count = 0;
	__enable_irq(); //开启中断
	return Temp;
}

//左电机编码器中断处理函数
void Encoder2_IRQHandler(void)
{
	if(EXTI_GetITStatus(EXTI_Line0) != RESET)
	{
		//简单防抖：使用软件计数器（更可靠）
		static uint32_t debounce_counter = 0;
		debounce_counter++;
		if(debounce_counter - Encoder2_Last_Time > 1) //防抖阈值1次中断（减少过滤）
		{
			uint8_t Current_A = GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0);
			uint8_t Current_B = GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1);

			//检测A相上升沿
			if(Current_A && !Encoder2_Last_A)
			{
				if(Current_B) Encoder2_Count++; //正转
				else Encoder2_Count--; //反转
			}
			//检测A相下降沿
			else if(!Current_A && Encoder2_Last_A)
			{
				if(!Current_B) Encoder2_Count++; //正转
				else Encoder2_Count--; //反转
			}

			//更新状态
			Encoder2_Last_A = Current_A;
			Encoder2_Last_B = Current_B;
			Encoder2_Last_Time = debounce_counter;
		}

		EXTI_ClearITPendingBit(EXTI_Line0); //清除中断标志
	}
}
