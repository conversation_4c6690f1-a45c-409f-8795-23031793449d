#include "SysConfig.h"

// 可选：启用传感器测试模式
// #define ENABLE_SENSOR_TEST_MODE

#ifdef ENABLE_SENSOR_TEST_MODE
// 测试函数声明
extern uint8_t Integration_Test_Run(void);
extern uint8_t Integration_Test_QuickCheck(void);
#endif

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    Task_Init();  // 项目原有任务初始化（包含电机、OLED、传感器等）

#ifdef ENABLE_SENSOR_TEST_MODE
    // 可选：运行传感器集成测试
    uint32_t test_start_time = Sys_GetTick();
    uint8_t test_result = Integration_Test_Run();
    uint32_t test_duration = Sys_GetTick() - test_start_time;

    // 测试结果可以通过OLED显示或其他方式输出
    // 这里只是示例，实际使用时可以根据需要修改
#endif

    while (1)
    {
        Task_Start(Sys_GetTick);

#ifdef ENABLE_SENSOR_TEST_MODE
        // 可选：定期进行快速检查
        static uint32_t last_check_time = 0;
        uint32_t current_time = Sys_GetTick();
        if(current_time - last_check_time > 10000) { // 每10秒检查一次
            uint8_t sensor_ok = Integration_Test_QuickCheck();
            last_check_time = current_time;
            // 可以根据检查结果采取相应措施
        }
#endif
    }
}
