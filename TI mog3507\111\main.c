#include "SysConfig.h"

/* 可选：启用传感器测试模式 */
/* #define ENABLE_SENSOR_TEST_MODE */

#ifdef ENABLE_SENSOR_TEST_MODE
/* 简化的测试函数声明 */
extern unsigned char Test_QuickCheck(void);
#endif

int main(void)
{
    /* 系统初始化 */
    SYSCFG_DL_init();
    Task_Init();  /* 项目原有任务初始化（包含电机、OLED、传感器等） */

    while (1)
    {
        Task_Start(Sys_GetTick);

#ifdef ENABLE_SENSOR_TEST_MODE
        /* 可选：定期进行快速检查 */
        {
            static uint32_t last_check_time = 0;
            uint32_t current_time = Sys_GetTick();
            if(current_time - last_check_time > 10000) { /* 每10秒检查一次 */
                unsigned char sensor_ok = Test_QuickCheck();
                last_check_time = current_time;
                /* 可以根据检查结果采取相应措施 */
            }
        }
#endif
    }
}
