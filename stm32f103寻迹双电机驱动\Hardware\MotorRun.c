#include "stm32f10x.h"
#include "Motor.h"
#include "PWM.h"

// 寻迹机器人运动控制模块，提供双电机差速控制功能

// 双电机前进，参数为速度百分比(0-100)
void dual_run(uint16_t LeftSpeed, uint16_t RightSpeed)
{
	PWM_SetCompare2(LeftSpeed * 200);  // 左电机PWM调速，200为满速对应值
	PWM_SetCompare1(RightSpeed * 200); // 右电机PWM调速
	Left_moto_go();                    // 左电机前进
	Right_moto_go();                   // 右电机前进
}

// 双电机后退，参数为速度百分比(0-100)
void dual_backrun(uint16_t LeftSpeed, uint16_t RightSpeed)
{
	PWM_SetCompare2(LeftSpeed * 200);  // 左电机PWM调速
	PWM_SetCompare1(RightSpeed * 200); // 右电机PWM调速
	Left_moto_back();                  // 左电机后退
	Right_moto_back();                 // 右电机后退
}

// 双电机停止
void dual_stop(void)
{
	Left_moto_Stop();  // 左电机停止
	Right_moto_Stop(); // 右电机停止
}

// 差速转向控制，BaseSpeed为基础速度，TurnRate为转向率(正值右转，负值左转)
void differential_turn(int16_t BaseSpeed, int16_t TurnRate)
{
	int16_t LeftSpeed = BaseSpeed - TurnRate;  // 左轮速度=基础速度-转向率
	int16_t RightSpeed = BaseSpeed + TurnRate; // 右轮速度=基础速度+转向率

	// 限制速度范围在0-100之间
	if(LeftSpeed < 0) LeftSpeed = 0;
	if(LeftSpeed > 100) LeftSpeed = 100;
	if(RightSpeed < 0) RightSpeed = 0;
	if(RightSpeed > 100) RightSpeed = 100;

	dual_run(LeftSpeed, RightSpeed); // 执行差速控制
}
