#ifndef __GRAYSCALE_H__
#define __GRAYSCALE_H__

#include "stm32f10x.h"

//传感器数据结构
typedef struct {
    uint16_t Analog_value[8];    // 原始模拟量值
    uint16_t Normal_value[8];    // 归一化后的值
    uint16_t Calibrated_white[8]; // 白校准基准值
    uint16_t Calibrated_black[8]; // 黑校准基准值
    uint16_t Gray_white[8];      // 白平衡灰度值
    uint16_t Gray_black[8];      // 黑平衡灰度值
    double Normal_factor[8];     // 归一化系数
    double bits;                 // ADC分辨率对应位数
    uint8_t Digital;             // 数字输出状态
    uint8_t Time_out;            // 超时标志
    uint8_t Tick;                // 时基计数器
    uint8_t ok;                  // 传感器就绪标志
} Grayscale_Sensor;

void Grayscale_Init(void);
void Grayscale_Sensor_Init_First(Grayscale_Sensor* sensor);
void Grayscale_ReadAll(Grayscale_Sensor* sensor);
uint8_t Grayscale_GetDigital(Grayscale_Sensor* sensor);
uint16_t* Grayscale_GetAnalog(Grayscale_Sensor* sensor);

#endif
