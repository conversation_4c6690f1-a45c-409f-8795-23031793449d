#ifndef __MOTORRUN_H__
#define __MOTORRUN_H__

// 寻迹机器人运动控制模块，提供双电机差速控制的核心功能
// 支持前进、后退、停止和差速转向四种基本运动模式

void dual_run(uint16_t LeftSpeed, uint16_t RightSpeed);        // 双电机前进，速度0-100%
void dual_backrun(uint16_t LeftSpeed, uint16_t RightSpeed);    // 双电机后退，速度0-100%
void dual_stop(void);                                          // 双电机停止
void differential_turn(int16_t BaseSpeed, int16_t TurnRate);   // 差速转向，TurnRate正值右转负值左转

#endif
